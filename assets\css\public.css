/**
 * Modern Public-facing styles for USF Auction Plugin
 * Mobile-first responsive design with modern UI elements
 */

/* CSS Custom Properties for consistent theming */
:root {
    --usf-primary: #dc2626;
    --usf-primary-hover: #b91c1c;
    --usf-primary-light: #fee2e2;
    --usf-secondary: #64748b;
    --usf-secondary-hover: #475569;
    --usf-success: #059669;
    --usf-success-light: #d1fae5;
    --usf-warning: #d97706;
    --usf-warning-light: #fef3c7;
    --usf-danger: #dc2626;
    --usf-danger-light: #fee2e2;
    --usf-gray-50: #f8fafc;
    --usf-gray-100: #f1f5f9;
    --usf-gray-200: #e2e8f0;
    --usf-gray-300: #cbd5e1;
    --usf-gray-400: #94a3b8;
    --usf-gray-500: #64748b;
    --usf-gray-600: #475569;
    --usf-gray-700: #334155;
    --usf-gray-800: #1e293b;
    --usf-gray-900: #0f172a;
    --usf-white: #ffffff;
    --usf-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --usf-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --usf-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --usf-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --usf-radius: 0.5rem;
    --usf-radius-lg: 0.75rem;
    --usf-radius-xl: 1rem;
}

/* Base Styles */
.usf-auctions-container,
.usf-single-auction {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--usf-gray-800);
}

/* Auction Grid Styles */
.usf-auctions-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
}

.usf-auction-filters {
    background: var(--usf-white);
    padding: 1.5rem;
    border-radius: var(--usf-radius-lg);
    margin-bottom: 2rem;
    box-shadow: var(--usf-shadow);
    border: 1px solid var(--usf-gray-200);
}

.usf-filter-form {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.usf-search-field {
    display: flex;
    gap: 0.75rem;
    align-items: center;
    flex: 1;
    min-width: 250px;
}

.usf-search-field input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 2px solid var(--usf-gray-200);
    border-radius: var(--usf-radius);
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: var(--usf-white);
}

.usf-search-field input:focus {
    outline: none;
    border-color: var(--usf-primary);
    box-shadow: 0 0 0 3px var(--usf-primary-light);
}

.usf-filter-fields {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.usf-filter-fields select {
    padding: 0.75rem 1rem;
    border: 2px solid var(--usf-gray-200);
    border-radius: var(--usf-radius);
    font-size: 0.875rem;
    background: var(--usf-white);
    transition: all 0.2s ease;
}

.usf-filter-fields select:focus {
    outline: none;
    border-color: var(--usf-primary);
    box-shadow: 0 0 0 3px var(--usf-primary-light);
}

.usf-auctions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.usf-auction-card {
    background: var(--usf-white);
    border: 1px solid var(--usf-gray-200);
    border-radius: var(--usf-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--usf-shadow);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.usf-auction-card:hover {
    box-shadow: var(--usf-shadow-lg);
    transform: translateY(-2px);
}

.usf-auction-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    gap: 1rem;
}

.usf-auction-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0;
    color: var(--usf-gray-900);
    line-height: 1.4;
}

.usf-auction-house {
    background: transparent;
    color: var(--usf-primary);
    padding: 0.375rem 0.75rem;
    border: 2px solid var(--usf-primary);
    border-radius: var(--usf-radius);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    white-space: nowrap;
    box-shadow: var(--usf-shadow-sm);
}

/* Auction House Link Styles */
.usf-auction-house-link {
    color: inherit;
    text-decoration: none;
    display: inline-block;
    transition: all 0.2s ease-in-out;
    position: relative;
}

.usf-auction-house-link:hover {
    color: inherit;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: var(--usf-shadow-md);
}

.usf-auction-house-link:focus {
    outline: 2px solid var(--usf-white);
    outline-offset: 2px;
}

/* Add a subtle indicator that the auction house is clickable */
.usf-auction-house .usf-auction-house-link::after {
    content: '→';
    opacity: 0;
    margin-left: 0.25rem;
    transition: opacity 0.2s ease-in-out;
    font-size: 0.8em;
}

.usf-auction-house:hover .usf-auction-house-link::after {
    opacity: 0.7;
}

/* Non-clickable auction house names */
.usf-auction-house-name {
    display: inline-block;
}

.usf-auction-details {
    margin-bottom: 1.25rem;
}

.usf-detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    padding: 0.25rem 0;
}

.usf-detail-row:not(:last-child) {
    border-bottom: 1px solid var(--usf-gray-100);
}

.usf-label {
    font-weight: 500;
    color: var(--usf-gray-600);
}

.usf-value {
    color: var(--usf-gray-900);
    font-weight: 500;
}

.usf-auction-timer {
    margin-bottom: 1.25rem;
    padding: 1rem;
    background: linear-gradient(135deg, var(--usf-primary-light), var(--usf-gray-50));
    border-radius: var(--usf-radius);
    text-align: center;
    border: 1px solid var(--usf-gray-200);
}

.usf-countdown {
    font-weight: 600;
}

.usf-timer-label {
    display: block;
    font-size: 0.75rem;
    color: var(--usf-gray-600);
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    font-weight: 500;
}

.usf-timer-display {
    font-size: 1rem;
    color: var(--usf-primary);
    font-weight: 700;
}

.usf-timer-expired {
    color: var(--usf-danger);
    font-weight: 600;
    font-size: 0.875rem;
}

.usf-auction-actions {
    display: flex;
    gap: 0.75rem;
}

/* Inline Quick Bid Styles */
.usf-quick-bid-inline {
    background: var(--usf-gray-50);
    border: 2px solid var(--usf-primary-light);
    border-radius: var(--usf-radius);
    padding: 1rem;
    margin-top: 1rem;
    animation: slideDown 0.3s ease-out;
}

.usf-quick-bid-inline h4 {
    margin: 0 0 0.75rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--usf-primary);
}

.usf-quick-bid-inline .usf-form-group {
    margin-bottom: 0.75rem;
}

.usf-quick-bid-inline .usf-form-group label {
    display: block;
    margin-bottom: 0.25rem;
    font-weight: 500;
    color: var(--usf-gray-700);
    font-size: 0.8rem;
}

.usf-quick-bid-inline input[type="number"] {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--usf-gray-300);
    border-radius: var(--usf-radius);
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.usf-quick-bid-inline input[type="number"]:focus {
    outline: none;
    border-color: var(--usf-primary);
    box-shadow: 0 0 0 2px var(--usf-primary-light);
}

.usf-checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    font-size: 0.8rem;
    line-height: 1.4;
    color: var(--usf-gray-700);
}

.usf-checkbox-label input[type="checkbox"] {
    margin: 0;
    width: auto;
    flex-shrink: 0;
}

.usf-quick-bid-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.75rem;
}

.usf-btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    min-height: 2.25rem;
}

.usf-quick-bid-messages {
    margin-top: 0.75rem;
}

.usf-quick-bid-messages .usf-message {
    padding: 0.5rem 0.75rem;
    border-radius: var(--usf-radius);
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
}

/* Animation for inline form */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
        max-height: 0;
    }
    to {
        opacity: 1;
        transform: translateY(0);
        max-height: 300px;
    }
}

/* Loading spinner for inline form */
.usf-spinner {
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid var(--usf-white);
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Modern Button Styles */
.usf-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border: 2px solid var(--usf-primary);
    border-radius: 0;
    cursor: pointer;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 600;
    text-align: center;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    min-height: 2.75rem;
}

.usf-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px var(--usf-primary-light);
}

.usf-btn-primary {
    background: var(--usf-white);
    color: var(--usf-primary);
    box-shadow: var(--usf-shadow-sm);
}

.usf-btn-primary:hover {
    background: var(--usf-primary-light);
    color: var(--usf-primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--usf-shadow-md);
}

.usf-btn-secondary {
    background: var(--usf-white);
    color: var(--usf-primary);
    border: 2px solid var(--usf-primary);
}

.usf-btn-secondary:hover {
    background: var(--usf-primary-light);
    color: var(--usf-primary-hover);
    border-color: var(--usf-gray-400);
    transform: translateY(-1px);
}

.usf-btn-outline {
    background: transparent;
    color: var(--usf-primary);
    border: 2px solid var(--usf-primary);
}

.usf-btn-outline:hover {
    background: var(--usf-primary);
    color: var(--usf-white);
    transform: translateY(-1px);
}

.usf-btn-large {
    padding: 1rem 2rem;
    font-size: 1rem;
    min-height: 3.5rem;
}

.usf-auction-closed {
    color: var(--usf-danger);
    font-weight: 600;
    font-style: italic;
    font-size: 0.875rem;
}

/* Single Auction Modern Styles */
.usf-single-auction {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
}

/* Hero Section */
.usf-single-auction .usf-auction-header {
    background: linear-gradient(135deg, var(--usf-primary), var(--usf-primary-hover));
    color: var(--usf-white);
    padding: 2rem;
    border-radius: var(--usf-radius-xl);
    margin-bottom: 2rem;
    box-shadow: var(--usf-shadow-lg);
}

.usf-single-auction .usf-auction-header h1,
.usf-single-auction .usf-auction-header h2 {
    margin: 0 0 0.5rem 0;
    font-size: 1.875rem;
    font-weight: 700;
    line-height: 1.2;
}

.usf-auction-meta {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.usf-lot-id {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: var(--usf-radius);
    font-weight: 600;
    font-size: 0.875rem;
}

.usf-single-auction .usf-auction-house {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: var(--usf-radius);
    font-weight: 600;
    font-size: 0.875rem;
}

/* Single auction page clickable auction house styles */
.usf-single-auction .usf-auction-house .usf-auction-house-link {
    color: var(--usf-white);
    transition: all 0.2s ease-in-out;
}

.usf-single-auction .usf-auction-house:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.usf-single-auction .usf-auction-house .usf-auction-house-link:hover {
    color: var(--usf-white);
}

.usf-single-auction .usf-auction-house .usf-auction-house-link:focus {
    outline: 2px solid var(--usf-white);
    outline-offset: 2px;
}

/* Auction house link in Lot Details table */
.usf-auction-summary table .usf-auction-house-link {
    color: var(--usf-primary);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.2s ease-in-out;
    border-bottom: 1px solid transparent;
}

.usf-auction-summary table .usf-auction-house-link:hover {
    color: var(--usf-primary-hover);
    text-decoration: none;
    border-bottom-color: var(--usf-primary-hover);
    transform: translateX(2px);
}

.usf-auction-summary table .usf-auction-house-link:focus {
    outline: 2px solid var(--usf-primary);
    outline-offset: 2px;
    border-radius: 2px;
}

/* Descriptive text styling for auction house info */
.usf-auction-summary table small {
    display: block;
    margin-top: 0.5rem;
    line-height: 1.4;
}

/* Main Content Grid */
.usf-auction-content {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 2rem;
    margin-bottom: 2rem;
}

.usf-auction-left,
.usf-auction-main {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.usf-auction-right,
.usf-auction-sidebar {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* Modern Card Styles */
.usf-auction-summary,
.usf-time-remaining,
.usf-bid-form-container,
.usf-user-bid-summary {
    background: var(--usf-white);
    border: 1px solid var(--usf-gray-200);
    border-radius: var(--usf-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--usf-shadow);
}

.usf-time-remaining {
    padding-bottom: 2.5rem;
    margin: 0px 0px 28px 0px;
}

.usf-auction-summary h3,
.usf-time-remaining h3,
.usf-bid-form-container h3 {
    margin: 0 0 1rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--usf-gray-900);
    border-bottom: 2px solid var(--usf-gray-100);
    padding-bottom: 0.75rem;
}

/* Modern Table Styles */
.usf-summary-table {
    width: 100%;
    border-collapse: collapse;
}

.usf-summary-table td {
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--usf-gray-100);
    font-size: 0.875rem;
}

.usf-summary-table td:first-child {
    font-weight: 600;
    color: var(--usf-gray-600);
    width: 40%;
}

.usf-summary-table td:last-child {
    color: var(--usf-gray-900);
    font-weight: 500;
}

/* Enhanced Countdown Timer */
.usf-countdown-large {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.75rem;
    margin-top: 1rem;
}

.usf-countdown-item {
    text-align: center;
    padding: 1rem 0.5rem;
    background: linear-gradient(135deg, var(--usf-gray-50), var(--usf-white));
    border-radius: var(--usf-radius);
    border: 1px solid var(--usf-gray-200);
    box-shadow: var(--usf-shadow-sm);
}

.usf-countdown-item .usf-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--usf-primary);
    line-height: 1;
}

.usf-countdown-item .usf-label {
    display: block;
    font-size: 0.75rem;
    color: var(--usf-gray-600);
    margin-top: 0.25rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    font-weight: 500;
}

.usf-expired h3 {
    color: var(--usf-danger) !important;
    text-align: center;
    font-size: 1.5rem;
}

/* Modern Form Styles */
.usf-form-group {
    margin-bottom: 1.25rem;
}

.usf-form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--usf-gray-700);
    font-size: 0.875rem;
}

.usf-form-group input,
.usf-form-group select,
.usf-form-group textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid var(--usf-gray-200);
    border-radius: var(--usf-radius);
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: var(--usf-white);
}

.usf-form-group input:focus,
.usf-form-group select:focus,
.usf-form-group textarea:focus {
    outline: none;
    border-color: var(--usf-primary);
    box-shadow: 0 0 0 3px var(--usf-primary-light);
}

.usf-checkbox-label {
    display: flex !important;
    align-items: flex-start;
    gap: 0.75rem;
    cursor: pointer;
    font-size: 0.875rem;
    line-height: 1.5;
}

.usf-checkbox-label input[type="checkbox"] {
    width: auto !important;
    margin: 0;
    margin-top: 0.125rem;
    flex-shrink: 0;
}

/* Bid Status Styles */
.usf-user-bid-status,
.usf-bid-status {
    border-radius: var(--usf-radius-lg);
    padding: 1.25rem;
    margin-bottom: 1.5rem;
}

/* Bid Badges for Auction Cards */
.usf-bid-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    border-radius: var(--usf-radius);
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.75rem;
}

.usf-bid-badge.usf-bid-pending {
    background: var(--usf-warning-light);
    color: var(--usf-warning);
    border: 1px solid #fbbf24;
}

.usf-bid-badge.usf-bid-accepted {
    background: var(--usf-success-light);
    color: var(--usf-success);
    border: 1px solid #10b981;
}

.usf-bid-badge.usf-bid-rejected {
    background: var(--usf-danger-light);
    color: var(--usf-danger);
    border: 1px solid #f87171;
}

.usf-badge-icon {
    font-size: 1rem;
    line-height: 1;
}

.usf-badge-text {
    font-weight: 600;
}

.usf-bid-status-summary {
    text-align: center;
}

.usf-bid-status-summary h4 {
    margin: 0 0 1rem 0;
    font-size: 1.125rem;
    font-weight: 600;
}

.usf-bid-status-summary.usf-pending {
    background: var(--usf-warning-light);
    border: 1px solid #fbbf24;
}

.usf-bid-status-summary.usf-pending h4 {
    color: var(--usf-warning);
}

.usf-bid-status-summary.usf-accepted {
    background: var(--usf-success-light);
    border: 1px solid #10b981;
}

.usf-bid-status-summary.usf-accepted h4 {
    color: var(--usf-success);
}

.usf-bid-status-summary.usf-rejected {
    background: var(--usf-danger-light);
    border: 1px solid #f87171;
}

.usf-bid-status-summary.usf-rejected h4 {
    color: var(--usf-danger);
}

/* Modern Items Table */
.usf-auction-items {
    margin-top: 2rem;
}

.usf-auction-items h3 {
    margin: 0 0 1.5rem 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--usf-gray-900);
    text-align: center;
    position: relative;
}

.usf-auction-items h3::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 4rem;
    height: 3px;
    background: linear-gradient(135deg, var(--usf-primary), var(--usf-primary-hover));
    border-radius: 2px;
}

.usf-items-table-container {
    background: var(--usf-white);
    border: 1px solid var(--usf-gray-200);
    border-radius: var(--usf-radius-lg);
    overflow: hidden;
    box-shadow: var(--usf-shadow);
}

.usf-items-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--usf-white);
}

.usf-items-table th {
    background: linear-gradient(135deg, var(--usf-gray-50), var(--usf-gray-100));
    padding: 1rem 0.75rem;
    text-align: left;
    font-weight: 600;
    color: var(--usf-gray-700);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    border-bottom: 2px solid var(--usf-gray-200);
}

.usf-items-table td {
    padding: 0.875rem 0.75rem;
    border-bottom: 1px solid var(--usf-gray-100);
    font-size: 0.875rem;
    color: var(--usf-gray-800);
}

.usf-items-table tr:hover {
    background: var(--usf-gray-50);
}

.usf-items-table tr:last-child td {
    border-bottom: none;
}

/* Status Badges */
.usf-status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    border-radius: var(--usf-radius);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.usf-status-badge.usf-status-active {
    background: var(--usf-success-light);
    color: var(--usf-success);
    border: 1px solid #10b981;
}

.usf-status-badge.usf-status-closed {
    background: var(--usf-danger-light);
    color: var(--usf-danger);
    border: 1px solid #f87171;
}

/* Pagination Styles */
.usf-pagination {
    display: flex;
    justify-content: center;
    gap: 0.25rem;
    margin-top: 2rem;
    flex-wrap: wrap;
}

.usf-page-link {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1rem;
    border: 1px solid var(--usf-gray-300);
    color: var(--usf-gray-700);
    text-decoration: none;
    border-radius: var(--usf-radius);
    transition: all 0.2s ease;
    font-weight: 500;
    min-width: 2.75rem;
}

.usf-page-link:hover {
    background: var(--usf-primary);
    color: var(--usf-white);
    border-color: var(--usf-primary);
    transform: translateY(-1px);
}

.usf-page-link.usf-current {
    background: var(--usf-primary);
    color: var(--usf-white);
    border-color: var(--usf-primary);
}

.usf-page-dots {
    display: flex;
    align-items: center;
    padding: 0.75rem 0.5rem;
    color: var(--usf-gray-400);
}

/* Loading States */
.usf-loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.usf-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 2rem;
    height: 2rem;
    border: 3px solid var(--usf-gray-200);
    border-top: 3px solid var(--usf-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Message Styles */
.usf-message {
    padding: 1rem 1.25rem;
    border-radius: var(--usf-radius);
    margin-bottom: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.usf-message.success {
    background: var(--usf-success-light);
    color: var(--usf-success);
    border: 1px solid #10b981;
}

.usf-message.error {
    background: var(--usf-danger-light);
    color: var(--usf-danger);
    border: 1px solid #f87171;
}

/* No Auctions Message */
.usf-no-auctions {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--usf-gray-500);
    background: var(--usf-gray-50);
    border-radius: var(--usf-radius-lg);
    border: 2px dashed var(--usf-gray-300);
}

.usf-no-auctions p {
    font-size: 1.125rem;
    margin: 0;
}

/* Login Actions */
.usf-login-actions {
    display: flex;
    gap: 0.75rem;
    margin-top: 1rem;
}

.usf-login-actions .usf-btn {
    flex: 1;
}

/* Responsive Design - Mobile First */
@media (max-width: 768px) {
    .usf-auctions-container,
    .usf-single-auction {
        padding: 0.75rem;
    }
    
    .usf-auctions-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .usf-auction-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .usf-filter-form {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }
    
    .usf-search-field {
        flex-direction: column;
        min-width: auto;
    }
    
    .usf-countdown-large {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }
    
    .usf-countdown-item .usf-number {
        font-size: 1.25rem;
    }
    
    .usf-auction-actions {
        flex-direction: column;
    }
    
    .usf-single-auction .usf-auction-header {
        padding: 1.5rem;
        text-align: center;
    }
    
    .usf-single-auction .usf-auction-header h1,
    .usf-single-auction .usf-auction-header h2 {
        font-size: 1.5rem;
    }
    
    .usf-auction-meta {
        justify-content: center;
    }
    
    .usf-items-table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .usf-items-table {
        min-width: 600px;
    }
    
    .usf-pagination {
        gap: 0.125rem;
    }
    
    .usf-page-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }
}

@media (max-width: 480px) {
    .usf-auction-card {
        padding: 1rem;
    }
    
    .usf-auction-header {
        flex-direction: column;
        gap: 0.75rem;
        text-align: center;
    }
    
    .usf-auction-house {
        align-self: center;
    }
    
    .usf-countdown-large {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .usf-countdown-item {
        padding: 0.75rem 0.25rem;
    }
    
    .usf-countdown-item .usf-number {
        font-size: 1rem;
    }
    
    .usf-countdown-item .usf-label {
        font-size: 0.625rem;
    }
    
    .usf-login-actions {
        flex-direction: column;
    }
}

/* Print Styles */
@media print {
    .usf-auction-actions,
    .usf-bid-form-container,
    .usf-pagination {
        display: none !important;
    }
    
    .usf-single-auction,
    .usf-auction-card {
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --usf-primary: #0000ff;
        --usf-primary-hover: #0000cc;
        --usf-gray-200: #cccccc;
        --usf-gray-300: #999999;
        --usf-gray-600: #333333;
        --usf-gray-800: #000000;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus Visible Support */
.usf-btn:focus-visible,
.usf-form-group input:focus-visible,
.usf-form-group select:focus-visible,
.usf-form-group textarea:focus-visible {
    outline: 2px solid var(--usf-primary);
    outline-offset: 2px;
}

/* Additional Modern Enhancements */
.usf-auction-timer .usf-countdown {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.usf-auction-timer .usf-countdown span {
    font-variant-numeric: tabular-nums;
}

/* Smooth scrolling for anchor links */
html {
    scroll-behavior: smooth;
}

/* Enhanced accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* New Modern Components */

/* Current Bid Information Section */
.usf-current-bid-info {
    background: linear-gradient(135deg, var(--usf-primary-light), var(--usf-gray-50));
    border: 1px solid var(--usf-primary);
    border-radius: var(--usf-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--usf-shadow);
    margin-bottom: 1.5rem;
}

.usf-current-bid-info h3 {
    margin: 0 0 1rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--usf-primary);
    border-bottom: 2px solid var(--usf-primary);
    padding-bottom: 0.75rem;
}

.usf-bid-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.usf-bid-stat-card {
    background: var(--usf-white);
    padding: 1rem;
    border-radius: var(--usf-radius);
    text-align: center;
    box-shadow: var(--usf-shadow-sm);
    border: 1px solid var(--usf-gray-200);
    transition: all 0.2s ease;
}

.usf-bid-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--usf-shadow-md);
}

.usf-stat-label {
    display: block;
    font-size: 0.75rem;
    color: var(--usf-gray-600);
    text-transform: uppercase;
    letter-spacing: 0.025em;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.usf-stat-value {
    display: block;
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--usf-primary);
    line-height: 1.2;
}

.usf-stat-value.usf-expired {
    color: var(--usf-danger);
}

.usf-stat-note {
    font-size: 0.625rem;
    color: var(--usf-gray-500);
    margin-top: 0.25rem;
    font-style: italic;
}

/* Grade Badge */
.usf-grade-badge {
    background: var(--usf-success-light);
    color: var(--usf-success);
    padding: 0.25rem 0.5rem;
    border-radius: var(--usf-radius);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

/* Shipping Information Section */
.usf-shipping-info {
    background: var(--usf-white);
    border: 1px solid var(--usf-gray-200);
    border-radius: var(--usf-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--usf-shadow);
}

.usf-shipping-info h3 {
    margin: 0 0 1rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--usf-gray-900);
    border-bottom: 2px solid var(--usf-gray-100);
    padding-bottom: 0.75rem;
}

.usf-info-grid {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.usf-info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--usf-gray-100);
    font-size: 0.875rem;
}

.usf-info-item:last-child {
    border-bottom: none;
}

.usf-info-label {
    font-weight: 500;
    color: var(--usf-gray-600);
}

.usf-info-value {
    font-weight: 500;
    color: var(--usf-gray-900);
}

/* Enhanced Manifest Section */
.usf-manifest-summary {
    background: var(--usf-gray-50);
    border: 1px solid var(--usf-gray-200);
    border-radius: var(--usf-radius);
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.usf-manifest-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
}

.usf-manifest-stat {
    text-align: center;
}

.usf-manifest-stat .usf-stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--usf-primary);
    line-height: 1;
}

.usf-manifest-stat .usf-stat-label {
    display: block;
    font-size: 0.75rem;
    color: var(--usf-gray-600);
    text-transform: uppercase;
    letter-spacing: 0.025em;
    font-weight: 500;
    margin-top: 0.25rem;
}

/* Enhanced Button Styles */
.usf-btn {
    position: relative;
    overflow: hidden;
}

.usf-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.usf-btn:hover::before {
    left: 100%;
}

/* Search and Filter Button Styles */
.usf-search-field button,
.usf-filter-fields button {
    background: var(--usf-primary);
    color: var(--usf-white);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--usf-radius);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.usf-search-field button:hover,
.usf-filter-fields button:hover {
    background: var(--usf-primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--usf-shadow-md);
}

/* Enhanced Form Actions */
.usf-form-actions {
    margin-top: 1.5rem;
}

/* Responsive Enhancements for New Components */
@media (max-width: 768px) {
    .usf-bid-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .usf-manifest-stats {
        gap: 1rem;
    }

    .usf-info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .usf-current-bid-info,
    .usf-shipping-info {
        padding: 1rem;
    }

    /* Inline quick bid responsive styles */
    .usf-quick-bid-inline {
        padding: 0.75rem;
    }

    .usf-quick-bid-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .usf-quick-bid-actions .usf-btn {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .usf-bid-stats {
        grid-template-columns: 1fr;
    }
    
    .usf-manifest-stats {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .usf-stat-value {
        font-size: 1rem;
    }
    
    .usf-manifest-stat .usf-stat-number {
        font-size: 1.25rem;
    }
}

/* Loading Animation for Bid Stats */
.usf-bid-stat-card.loading {
    position: relative;
}

.usf-bid-stat-card.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Enhanced Table Hover Effects */
.usf-items-table tbody tr {
    transition: all 0.2s ease;
}

.usf-items-table tbody tr:hover {
    background: var(--usf-primary-light);
    transform: scale(1.01);
}

/* Improved Status Badge Variants */
.usf-grade-badge.grade-a {
    background: var(--usf-success-light);
    color: var(--usf-success);
}

.usf-grade-badge.grade-b {
    background: var(--usf-warning-light);
    color: var(--usf-warning);
}

.usf-grade-badge.grade-c {
    background: var(--usf-danger-light);
    color: var(--usf-danger);
}

/* Enhanced Countdown Animation */
.usf-countdown-item {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Disable animation when auction is about to end */
.usf-countdown-urgent .usf-countdown-item {
    animation: urgentPulse 0.5s infinite;
    border-color: var(--usf-danger);
}

@keyframes urgentPulse {
    0%, 100% { 
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(220, 38, 38, 0.7);
    }
    50% { 
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(220, 38, 38, 0);
    }
}
