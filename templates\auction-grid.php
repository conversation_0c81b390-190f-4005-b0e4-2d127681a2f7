<?php
/**
 * Template for displaying auction grid (allauctions shortcode)
 *
 * @package USF_Auction
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="usf-auctions-container">
    <?php if (!empty($search_form)): ?>
    <!-- Search and Filter Form -->
    <div class="usf-auction-filters">
        <form method="GET" class="usf-filter-form">
            <div class="usf-search-field">
                <label for="auction_search">Search:</label>
                <input type="text" 
                       id="auction_search" 
                       name="auction_search" 
                       value="<?php echo esc_attr($current_search); ?>" 
                       placeholder="Search by lot ID, model, or grade...">
            </div>
            
            <div class="usf-filter-fields">
                <select name="auction_house">
                    <option value="">All Auction Houses</option>
                    <?php foreach ($auction_houses as $house): ?>
                        <option value="<?php echo esc_attr($house); ?>" 
                                <?php selected($current_auction_house, $house); ?>>
                            <?php echo esc_html($house); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                
                <select name="sort_by">
                    <option value="closing_time" <?php selected($current_sort, 'closing_time'); ?>>Sort by Closing Time</option>
                    <option value="min_offer" <?php selected($current_sort, 'min_offer'); ?>>Sort by Min Offer</option>
                    <option value="lot_id" <?php selected($current_sort, 'lot_id'); ?>>Sort by Lot ID</option>
                    <option value="created_at" <?php selected($current_sort, 'created_at'); ?>>Sort by Date Added</option>
                </select>
                
                <button type="submit" class="usf-btn usf-btn-primary">Filter</button>
            </div>
        </form>
    </div>
    <?php endif; ?>

    <?php if (!empty($auctions)): ?>
    <!-- Auctions Grid -->
    <div class="usf-auctions-grid">
        <?php 
        // Get user bid statuses for all auctions if user is logged in
        $user_bid_statuses = array();
        if (is_user_logged_in()) {
            $lot_ids = wp_list_pluck($auctions, 'lot_id');
            $user_bid_statuses = USF_Database::get_user_bid_statuses($lot_ids, get_current_user_id());
        }
        ?>
        <?php foreach ($auctions as $auction): ?>
            <div class="usf-auction-card" data-lot-id="<?php echo esc_attr($auction->lot_id); ?>">
                <div class="usf-auction-header">
                    <h4 class="usf-auction-title">
                        Lot #<?php echo esc_html($auction->lot_id); ?>
                    </h4>
                    <span class="usf-auction-house">
                        <?php echo USF_Auction_House_Helper::get_auction_house_link($auction->auction_house); ?>
                    </span>
                </div>
                
                <div class="usf-auction-details">
                    <div class="usf-detail-row">
                        <span class="usf-label">Model:</span>
                        <span class="usf-value"><?php echo esc_html($auction->model); ?></span>
                    </div>
                    
                    <?php if ($auction->memory): ?>
                    <div class="usf-detail-row">
                        <span class="usf-label">Memory:</span>
                        <span class="usf-value"><?php echo esc_html($auction->memory); ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <div class="usf-detail-row">
                        <span class="usf-label">Grade:</span>
                        <span class="usf-value"><?php echo esc_html($auction->grade); ?></span>
                    </div>
                    
                    <div class="usf-detail-row">
                        <span class="usf-label">Units:</span>
                        <span class="usf-value"><?php echo esc_html($auction->total_units); ?></span>
                    </div>
                    
                    <div class="usf-detail-row">
                        <span class="usf-label">Min Offer:</span>
                        <span class="usf-value">$<?php echo number_format($auction->min_offer, 2); ?></span>
                    </div>
                    
                    <?php if (isset($bid_counts[$auction->lot_id])): ?>
                    <div class="usf-detail-row">
                        <span class="usf-label">Bids:</span>
                        <span class="usf-value"><?php echo intval($bid_counts[$auction->lot_id]); ?></span>
                    </div>
                    <?php endif; ?>
                </div>
                
                <!-- User Bid Status -->
                <?php if (is_user_logged_in() && isset($user_bid_statuses[$auction->lot_id])): ?>
                    <?php $user_bid = $user_bid_statuses[$auction->lot_id]; ?>
                    <div class="usf-user-bid-status">
                        <?php if ($user_bid->status === 'pending'): ?>
                            <div class="usf-bid-badge usf-bid-pending">
                                <span class="usf-badge-icon">⏳</span>
                                <span class="usf-badge-text">Bid Submitted ($<?php echo number_format($user_bid->bid_amount, 2); ?>)</span>
                            </div>
                        <?php elseif ($user_bid->status === 'accepted'): ?>
                            <div class="usf-bid-badge usf-bid-accepted">
                                <span class="usf-badge-icon">🎉</span>
                                <span class="usf-badge-text">Bid Accepted ($<?php echo number_format($user_bid->bid_amount, 2); ?>)</span>
                            </div>
                        <?php elseif ($user_bid->status === 'rejected'): ?>
                            <div class="usf-bid-badge usf-bid-rejected">
                                <span class="usf-badge-icon">❌</span>
                                <span class="usf-badge-text">Bid Rejected - Can Bid Again</span>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
                
                <!-- Countdown Timer -->
                <div class="usf-auction-timer">
                    <?php
                    $closing_time = strtotime($auction->closing_time);
                    $now = current_time('timestamp');
                    ?>

                    <?php if ($closing_time > $now): ?>
                        <span class="usf-timer-label">Time Remaining:</span>
                        <?php $js_time = date('c', strtotime($auction->closing_time . ' ' . wp_timezone_string())); ?>
                        <div class="usf-countdown" data-closing-time="<?php echo esc_attr($js_time); ?>">
                            <span class="usf-timer-display">
                                <span class="usf-days">0d</span>
                                <span class="usf-hours">0h</span>
                                <span class="usf-minutes">0m</span>
                                <span class="usf-seconds">0s</span>
                            </span>
                        </div>
                    <?php else: ?>
                        <span class="usf-timer-expired">Auction Closed</span>
                    <?php endif; ?>
                </div>
                
                <!-- Action Buttons -->
                <div class="usf-auction-actions">
                    <?php if ($closing_time > $now): ?>
                        <?php
                        // Get the configured single auction page
                        $single_auction_page_id = USF_Database::get_setting('single_auction_page', '');
                        if (!empty($single_auction_page_id)) {
                            $single_url = add_query_arg('lot_id', $auction->lot_id, get_permalink($single_auction_page_id));
                        } else {
                            // Fallback to current page if no single auction page is configured
                            $single_url = add_query_arg('lot_id', $auction->lot_id, get_permalink());
                        }
                        ?>
                        <a href="<?php echo esc_url($single_url); ?>" 
                           class="usf-btn usf-btn-primary">
                            Place Bid
                        </a>
                        <a href="<?php echo esc_url($single_url); ?>" 
                           class="usf-btn usf-btn-outline">
                            View Details
                        </a>
                    <?php else: ?>
                        <span class="usf-auction-closed">Auction Closed</span>
                    <?php endif; ?>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <!-- Pagination -->
    <?php if ($total_pages > 1): ?>
    <div class="usf-pagination">
        <?php
        $base_url = remove_query_arg('paged');
        
        // Previous page
        if ($current_page > 1):
            $prev_url = add_query_arg('paged', $current_page - 1, $base_url);
        ?>
            <a href="<?php echo esc_url($prev_url); ?>" class="usf-page-link">&laquo; Previous</a>
        <?php endif; ?>
        
        <?php
        // Page numbers
        $start_page = max(1, $current_page - 2);
        $end_page = min($total_pages, $current_page + 2);
        
        if ($start_page > 1):
        ?>
            <a href="<?php echo esc_url(add_query_arg('paged', 1, $base_url)); ?>" class="usf-page-link">1</a>
            <?php if ($start_page > 2): ?>
                <span class="usf-page-dots">...</span>
            <?php endif; ?>
        <?php endif; ?>
        
        <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
            <?php if ($i == $current_page): ?>
                <span class="usf-page-link usf-current"><?php echo $i; ?></span>
            <?php else: ?>
                <a href="<?php echo esc_url(add_query_arg('paged', $i, $base_url)); ?>" class="usf-page-link"><?php echo $i; ?></a>
            <?php endif; ?>
        <?php endfor; ?>
        
        <?php if ($end_page < $total_pages): ?>
            <?php if ($end_page < $total_pages - 1): ?>
                <span class="usf-page-dots">...</span>
            <?php endif; ?>
            <a href="<?php echo esc_url(add_query_arg('paged', $total_pages, $base_url)); ?>" class="usf-page-link"><?php echo $total_pages; ?></a>
        <?php endif; ?>
        
        <?php
        // Next page
        if ($current_page < $total_pages):
            $next_url = add_query_arg('paged', $current_page + 1, $base_url);
        ?>
            <a href="<?php echo esc_url($next_url); ?>" class="usf-page-link">Next &raquo;</a>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <?php else: ?>
    <!-- No Auctions Found -->
    <div class="usf-no-auctions">
        <h3>No auctions found</h3>
        <p>There are currently no active auctions matching your criteria.</p>
        <?php if (!empty($current_search) || !empty($current_auction_house)): ?>
            <p><a href="<?php echo esc_url(remove_query_arg(['auction_search', 'auction_house', 'paged'])); ?>" class="usf-btn usf-btn-primary">View All Auctions</a></p>
        <?php endif; ?>
    </div>
    <?php endif; ?>
</div>
