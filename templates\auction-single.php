<?php
/**
 * Template for displaying single auction (singleauction shortcode)
 *
 * @package USF_Auction
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

$closing_time = strtotime($auction->closing_time);
$now = current_time('timestamp');
$is_active = $closing_time > $now;
?>

<div class="usf-single-auction" data-lot-id="<?php echo esc_attr($auction->lot_id); ?>">
    <div class="usf-auction-content">
        <!-- Left Column: Auction Details -->
        <div class="usf-auction-main">
            <div class="usf-auction-header">
                <h2>Auction Lot #<?php echo esc_html($auction->lot_id); ?></h2>
                <span class="usf-auction-house"><?php echo USF_Auction_House_Helper::get_auction_house_link($auction->auction_house); ?></span>
            </div>
            
            <!-- Auction Summary -->
            <div class="usf-auction-summary">
                <h3>Lot Summary</h3>
                <table>
                    <tr>
                        <td>Lot ID:</td>
                        <td><?php echo esc_html($auction->lot_id); ?></td>
                    </tr>
                    <tr>
                        <td>Model:</td>
                        <td><?php echo esc_html($auction->model); ?></td>
                    </tr>
                    <?php if ($auction->memory): ?>
                    <tr>
                        <td>Memory:</td>
                        <td><?php echo esc_html($auction->memory); ?></td>
                    </tr>
                    <?php endif; ?>
                    <tr>
                        <td>Grade:</td>
                        <td><?php echo esc_html($auction->grade); ?></td>
                    </tr>
                    <tr>
                        <td>Total Units:</td>
                        <td><?php echo esc_html($auction->total_units); ?></td>
                    </tr>
                    <tr>
                        <td>Auction House:</td>
                        <td>
                            <?php echo USF_Auction_House_Helper::get_auction_house_link($auction->auction_house); ?>
                            <br><small style="color: #666; font-style: italic;">Click here to see full details of the auction house you are buying from. Get details like shipping times, responsibilities, grading, bidding policies, etc.</small>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <?php
                            // Check if auction has any bids to determine label
                            $has_bids = USF_Database::auction_has_bids($auction->lot_id);
                            $price_label = $has_bids ? 'Updated Price:' : 'Starting Price:';
                            echo esc_html($price_label);
                            ?>
                        </td>
                        <td>
                            <strong>
                                $<?php
                                // Display current highest bid or starting price (including all bids for consistency)
                                $display_price = USF_Database::get_auction_display_price_all_bids($auction->lot_id, $auction->min_offer);
                                echo number_format($display_price, 2);
                                ?>
                            </strong>
                        </td>
                    </tr>
                    <tr>
                        <td>Closing Time:</td>
                        <td><?php echo wp_date('M j, Y g:i A T', $closing_time); ?></td>
                    </tr>
                    <tr>
                        <td>Status:</td>
                        <td>
                            <?php if ($is_active): ?>
                                <span class="usf-status-badge usf-status-active">Active</span>
                            <?php else: ?>
                                <span class="usf-status-badge usf-status-closed">Closed</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                </table>

                <!-- Item Details (Manifest) -->
                <?php if (!empty($items)): ?>
                <div class="usf-auction-items">
                    <h3>Item Breakdown</h3>
                    <div class="usf-items-table-container">
                        <table class="usf-items-table">
                            <thead>
                                <tr>
                                    <th>Quantity</th>
                                    <th>Manufacturer</th>
                                    <th>Model</th>
                                    <th>Description</th>
                                    <?php if ($show_capacity): ?>
                                        <th>Capacity</th>
                                    <?php endif; ?>
                                    <?php if ($show_color): ?>
                                        <th>Color</th>
                                    <?php endif; ?>
                                    <?php if ($show_carrier): ?>
                                        <th>Carrier</th>
                                    <?php endif; ?>
                                    <?php if ($show_grade): ?>
                                        <th>Grade</th>
                                    <?php endif; ?>
                                    <?php if ($show_part_number): ?>
                                        <th>Part #</th>
                                    <?php endif; ?>
                                    <?php if ($show_category): ?>
                                        <th>Category</th>
                                    <?php endif; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($items as $item): ?>
                                <tr>
                                    <td><?php echo esc_html($item->quantity); ?></td>
                                    <td><?php echo esc_html($item->manufacturer); ?></td>
                                    <td><?php echo esc_html($item->model); ?></td>
                                    <td><?php echo esc_html($item->description); ?></td>
                                    <?php if ($show_capacity): ?>
                                        <td><?php echo esc_html($item->capacity); ?></td>
                                    <?php endif; ?>
                                    <?php if ($show_color): ?>
                                        <td><?php echo esc_html($item->color); ?></td>
                                    <?php endif; ?>
                                    <?php if ($show_carrier): ?>
                                        <td><?php echo esc_html($item->carrier); ?></td>
                                    <?php endif; ?>
                                    <?php if ($show_grade): ?>
                                        <td><?php echo esc_html($item->grade); ?></td>
                                    <?php endif; ?>
                                    <?php if ($show_part_number): ?>
                                        <td><?php echo esc_html($item->part_number); ?></td>
                                    <?php endif; ?>
                                    <?php if ($show_category): ?>
                                        <td><?php echo esc_html($item->category); ?></td>
                                    <?php endif; ?>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <div class="usf-items-summary">
                        <p><strong>Total Items:</strong> <?php echo array_sum(wp_list_pluck($items, 'quantity')); ?></p>
                        <p><strong>Unique SKUs:</strong> <?php echo count($items); ?></p>
                    </div>
                </div>
                <?php endif; ?>
            </div>
            
            <!-- Countdown Timer -->
            <?php if ($is_active): ?>
            <div class="usf-auction-timer">
                <h3>Time Remaining</h3>
                <?php $js_time = date('c', strtotime($lot->closing_time . ' ' . wp_timezone_string())); ?>
                <div class="usf-countdown-large" data-closing-time="<?php echo esc_attr($js_time); ?>">
                    <div class="usf-countdown-item usf-days">
                        <span class="usf-number">0</span>
                        <span class="usf-label">Days</span>
                    </div>
                    <div class="usf-countdown-item usf-hours">
                        <span class="usf-number">0</span>
                        <span class="usf-label">Hours</span>
                    </div>
                    <div class="usf-countdown-item usf-minutes">
                        <span class="usf-number">0</span>
                        <span class="usf-label">Minutes</span>
                    </div>
                    <div class="usf-countdown-item usf-seconds">
                        <span class="usf-number">0</span>
                        <span class="usf-label">Seconds</span>
                    </div>
                </div>
            </div>
            <?php else: ?>
            <div class="usf-auction-timer">
                <h3>Auction Ended</h3>
                <div class="usf-auction-ended">
                    <p>This auction has closed and is no longer accepting bids.</p>
                </div>
            </div>
            <?php endif; ?>
        </div>
        
        <!-- Right Column: Bid Form -->
        <div class="usf-auction-sidebar">
            <?php if ($is_active): ?>
                <?php 
                // Show user bid status if logged in
                if (is_user_logged_in()) {
                    $current_user = wp_get_current_user();
                    $user_bid_status = USF_Database::get_user_bid_status($auction->lot_id, $current_user->ID);
                    
                    if ($user_bid_status) {
                        echo '<div class="usf-user-bid-summary">';
                        if ($user_bid_status->status === 'pending') {
                            echo '<div class="usf-bid-status-summary usf-pending">';
                            echo '<h4>Your Bid Status</h4>';
                            echo '<p><strong>Status:</strong> Under Review</p>';
                            echo '<p><strong>Amount:</strong> $' . number_format($user_bid_status->bid_amount, 2) . '</p>';
                            echo '<p><strong>Submitted:</strong> ' . date('M j, Y g:i A', strtotime($user_bid_status->bid_time)) . '</p>';
                            echo '</div>';
                        } elseif ($user_bid_status->status === 'accepted') {
                            echo '<div class="usf-bid-status-summary usf-accepted">';
                            echo '<h4>🎉 Bid Accepted!</h4>';
                            echo '<p><strong>Winning Bid:</strong> $' . number_format($user_bid_status->bid_amount, 2) . '</p>';
                            if ($user_bid_status->woocommerce_order_id) {
                                echo '<p><a href="' . esc_url(wc_get_order_url($user_bid_status->woocommerce_order_id)) . '" class="usf-btn usf-btn-primary">Complete Payment</a></p>';
                            }
                            echo '</div>';
                        } elseif ($user_bid_status->status === 'rejected') {
                            echo '<div class="usf-bid-status-summary usf-rejected">';
                            echo '<h4>Previous Bid Not Accepted</h4>';
                            echo '<p><strong>Previous Bid:</strong> $' . number_format($user_bid_status->bid_amount, 2) . '</p>';
                            if (!empty($user_bid_status->admin_notes)) {
                                echo '<p><strong>Note:</strong> ' . esc_html($user_bid_status->admin_notes) . '</p>';
                            }
                            echo '<p><em>You can submit a new bid below.</em></p>';
                            echo '</div>';
                        }
                        echo '</div>';
                    }
                }
                ?>
                <?php include USF_AUCTION_PLUGIN_PATH . 'templates/bid-form.php'; ?>
            <?php else: ?>
                <div class="usf-bid-form-container">
                    <h3>Auction Closed</h3>
                    <p>This auction has ended and is no longer accepting bids.</p>
                    <p><a href="<?php echo esc_url(remove_query_arg('lot_id')); ?>" class="usf-btn usf-btn-primary">View Other Auctions</a></p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    
    <!-- Terms and Conditions -->
    <div class="usf-auction-terms">
        <h3>Terms and Conditions</h3>
        <div class="usf-terms-content">
            <?php 
            $terms_content = get_option('usf_terms_conditions', '');
            if ($terms_content) {
                echo wp_kses_post($terms_content);
            } else {
                echo '<p>By placing a bid, you agree to purchase the items if your bid is accepted. Payment must be completed within 48 hours of bid acceptance.</p>';
            }
            ?>
        </div>
    </div>
    
    <!-- Related Auctions -->
    <?php if (!empty($related_auctions)): ?>
    <div class="usf-related-auctions">
        <h3>Related Auctions</h3>
        <div class="usf-auctions-grid">
            <?php foreach ($related_auctions as $related): ?>
                <div class="usf-auction-card" data-lot-id="<?php echo esc_attr($related->lot_id); ?>">
                    <div class="usf-auction-header">
                        <h4 class="usf-auction-title">
                            Lot #<?php echo esc_html($related->lot_id); ?>
                        </h4>
                        <span class="usf-auction-house">
                            <?php echo USF_Auction_House_Helper::get_auction_house_link($related->auction_house); ?>
                        </span>
                    </div>
                    
                    <div class="usf-auction-details">
                        <div class="usf-detail-row">
                            <span class="usf-label">Model:</span>
                            <span class="usf-value"><?php echo esc_html($related->model); ?></span>
                        </div>
                        <div class="usf-detail-row">
                            <span class="usf-label">Min Offer:</span>
                            <span class="usf-value">$<?php echo number_format($related->min_offer, 2); ?></span>
                        </div>
                    </div>
                    
                    <div class="usf-auction-actions">
                        <?php
                        // Get the configured single auction page
                        $single_auction_page_id = USF_Database::get_setting('single_auction_page', '');
                        if (!empty($single_auction_page_id)) {
                            $single_url = add_query_arg('lot_id', $related->lot_id, get_permalink($single_auction_page_id));
                        } else {
                            // Fallback to current page if no single auction page is configured
                            $single_url = add_query_arg('lot_id', $related->lot_id, get_permalink());
                        }
                        ?>
                        <a href="<?php echo esc_url($single_url); ?>" 
                           class="usf-btn usf-btn-outline">
                            View Details
                        </a>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>
</div>
